#!/bin/bash
set -euo pipefail

# 检查是否以 root 用户运行
if [ "$(id -u)" -ne 0 ]; then
    echo "请以 root 用户或使用 sudo 运行该脚本"
    exit 1
fi

# 定义工作目录（脚本所在目录）
WORKDIR=$(pwd)

# 更新软件包列表并安装必要的软件包
apt update
apt install -y unzip wget ufw

# 下载 Snell 服务器压缩包（如果不存在）
ZIP_FILE="snell-server-v4.1.1-linux-amd64.zip"
if [ ! -f "$ZIP_FILE" ]; then
    echo "下载 Snell 服务器..."
    wget https://dl.nssurge.com/snell/snell-server-v4.1.1-linux-amd64.zip
else
    echo "$ZIP_FILE 已存在，跳过下载。"
fi

# 解压 Snell 服务器（如果二进制文件不存在）
if [ ! -f "snell-server" ]; then
    echo "解压 Snell 服务器..."
    unzip "$ZIP_FILE"
else
    echo "snell-server 二进制文件已存在，跳过解压。"
fi

# 创建或覆盖 snell-server.conf 配置文件（如果已存在则备份）
CONFIG_FILE="snell-server.conf"
if [ -f "$CONFIG_FILE" ]; then
    echo "配置文件 $CONFIG_FILE 已存在，备份为 ${CONFIG_FILE}.bak"
    cp "$CONFIG_FILE" "${CONFIG_FILE}.bak"
fi

cat > "$CONFIG_FILE" <<EOF
[snell-server]
listen = 0.0.0.0:27958
psk = YvEpAhRVIwBhyypQ9FnX97LThMQ5nbB
ipv6 = false
EOF
echo "配置文件 $CONFIG_FILE 已生成。"

# 设置防火墙规则
# 开放所有 UDP 端口
ufw allow proto udp from any to any
# 开放 TCP 27958 端口（Snell 服务）
ufw allow 27958/tcp
# 开放 TCP 22 端口 (SSH)
ufw allow 22/tcp

# 如果 UFW 未启用，则启用（使用 --force 自动确认）
UFW_STATUS=$(ufw status | head -n 1)
if [[ "$UFW_STATUS" =~ "inactive" ]]; then
    echo "启用 UFW 防火墙..."
    ufw --force enable
fi
echo "当前防火墙规则："
ufw status verbose

# 为 snell-server 添加执行权限
chmod +x snell-server

# 创建 systemd 服务文件，管理 Snell 服务
SERVICE_FILE="/etc/systemd/system/snell-server.service"
cat > "$SERVICE_FILE" <<EOF
[Unit]
Description=Snell Server
After=network.target

[Service]
ExecStart=${WORKDIR}/snell-server -c ${WORKDIR}/snell-server.conf
WorkingDirectory=${WORKDIR}
Restart=on-failure
User=root

[Install]
WantedBy=multi-user.target
EOF
echo "systemd 服务文件已创建：$SERVICE_FILE"

# 重新加载 systemd 配置，并启用、启动服务
systemctl daemon-reload
systemctl enable snell-server.service
systemctl restart snell-server.service

# 检查服务状态
echo "Snell Server 服务状态："
systemctl status snell-server.service --no-pager

# 通过 ps 命令确认进程是否在运行
echo "当前 snell-server 进程："
ps aux | grep snell-server | grep -v grep

# 清理工作：删除压缩包（如需保留可注释掉下面一行）
rm -f "$ZIP_FILE"

echo "Snell Server 安装并启动成功。"
