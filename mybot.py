from pyrogram import Client, filters, idle
import subprocess
import re
import os
import logging
import asyncio
import time
import atexit
import glob
from datetime import datetime

# 设置日志输出
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Telegram API 参数
api_id = '25432929'
api_hash = '965c5d22f0b9d1d0326e84bbb2bb18c1'
bot_token = '6695214653:AAHrITsgddRHwNidAPxUYDCzLCtZLqOtIdo'

# 使用 Pyrogram 建立机器人，session 文件名为 "my_subscription_bot.session"
app = Client("my_subscription_bot", api_id=api_id, api_hash=api_hash, bot_token=bot_token)

# 全局变量：记录第一个建立会话的用户 chat_id
primary_chat_id = None
scanning_task = None

def is_valid_url(url: str) -> bool:
    """
    判断字符串是否为一个有效的 URL。
    """
    regex = re.compile(r'^(https?|ftp)://[^\s/$.?#].[^\s]*$', re.IGNORECASE)
    return re.match(regex, url) is not None

def get_video_info(video_path):
    """
    利用 ffmpeg/ffprobe 生成缩略图，并获取视频的宽、高以及时长。
    """
    thumb_path = video_path + ".jpg"
    cmd_thumbnail = ["ffmpeg", "-i", video_path, "-ss", "00:00:05", "-vframes", "1", thumb_path]
    subprocess.run(cmd_thumbnail, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    cmd_dimensions = [
        "ffprobe", "-v", "error", "-select_streams", "v:0",
        "-show_entries", "stream=width,height",
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    dimensions_output = subprocess.run(cmd_dimensions, stdout=subprocess.PIPE, text=True).stdout.splitlines()
    
    cmd_duration = [
        "ffprobe", "-v", "error", "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1", video_path
    ]
    duration_output = subprocess.run(cmd_duration, stdout=subprocess.PIPE, text=True).stdout.strip()
    
    try:
        if dimensions_output and duration_output:
            return {
                'width': int(dimensions_output[0]),
                'height': int(dimensions_output[1]),
                'duration': int(float(duration_output)),
                'thumb_path': thumb_path if os.path.exists(thumb_path) else None
            }
        else:
            return None
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

async def read_stream(stream, log_prefix):
    """
    异步读取子进程的输出流，防止缓冲区堵塞。
    """
    while True:
        line = await stream.readline()
        if not line:
            break
        logger.info(f"{log_prefix}: {line.decode().strip()}")

async def convert_ts_to_mp4(ts_path):
    """
    异步调用 ffmpeg 将 .ts 文件转为 .mp4 格式，
    并通过“-progress pipe:1”实时读取转码进度，同时添加超时机制。
    """
    mp4_path = ts_path.rsplit('.', 1)[0] + '.mp4'
    cmd = ["ffmpeg", "-y", "-i", ts_path, "-c", "copy", "-progress", "pipe:1", mp4_path]
    logger.info(f"开始转码: {ts_path} -> {mp4_path}")
    process = await asyncio.create_subprocess_exec(*cmd,
                                                    stdout=asyncio.subprocess.PIPE,
                                                    stderr=asyncio.subprocess.PIPE)
    # 启动异步读取 stderr 流，防止缓冲区堵塞
    stderr_task = asyncio.create_task(read_stream(process.stderr, "转码错误输出"))
    
    try:
        while True:
            try:
                # 每次读取超时设置为 5 秒，若超时则取消任务
                line = await asyncio.wait_for(process.stdout.readline(), timeout=5)
            except asyncio.TimeoutError:
                logger.error("等待 ffmpeg 输出超时，取消转换任务")
                process.kill()
                await process.wait()
                raise Exception("转换任务超时")
            if not line:
                break
            line = line.decode().strip()
            logger.info(f"转码进度: {line}")
    finally:
        # 确保 stderr_task 完成
        await stderr_task
    await process.wait()
    if process.returncode != 0:
        err = await process.stderr.read()
        error_message = err.decode()
        logger.error(f"转码错误: {error_message}")
        raise Exception(f"ffmpeg 转码失败，错误信息: {error_message}")
    logger.info(f"转码完成: {mp4_path}")
    return mp4_path

async def safe_convert(ts_path, retries=3):
    """
    包装转换函数，为转换任务增加重试机制。
    """
    for attempt in range(1, retries+1):
        try:
            mp4_path = await convert_ts_to_mp4(ts_path)
            return mp4_path
        except Exception as e:
            logger.error(f"转换失败，第 {attempt} 次尝试: {e}")
            if attempt < retries:
                await asyncio.sleep(5)
            else:
                raise e

def upload_progress(current, total):
    """
    上传进度回调函数，输出上传进度百分比。
    """
    if total:
        percent = (current / total) * 100
        logger.info(f"上传进度: {current}/{total} 字节 ({percent:.2f}%)")
    else:
        logger.info(f"上传进度: {current} 字节")

async def check_and_upload_new_files():
    """
    循环扫描程序所在目录下 downloads/抖音直播 文件夹（含所有子目录），
    对未在录制中的 .ts 文件进行转码并上传，期间输出各环节进度日志。
    """
    global primary_chat_id
    base_dir = os.path.join(os.getcwd(), "downloads", "抖音直播")
    modification_threshold = 30  # 30秒内修改的文件认为还在录制中
    while True:
        try:
            ts_files = []
            for root, dirs, files in os.walk(base_dir):
                for file in files:
                    if file.endswith(".ts"):
                        ts_files.append(os.path.join(root, file))
            if ts_files:
                for ts_path in ts_files:
                    if not os.path.exists(ts_path):
                        logger.info(f"文件不存在，跳过：{ts_path}")
                        continue
                    last_modified = os.path.getmtime(ts_path)
                    if time.time() - last_modified < modification_threshold:
                        logger.info(f"文件 {ts_path} 最近 {modification_threshold} 秒内有修改，可能还在录制中，跳过处理。")
                        continue
                    logger.info(f"开始处理文件: {ts_path}")
                    try:
                        mp4_path = await safe_convert(ts_path)
                    except Exception as e:
                        logger.error(f"文件转换失败，跳过此文件: {ts_path}，错误: {e}")
                        continue
                    
                    try:
                        # 上传原始 ts 文件
                        logger.info(f"开始上传文档: {ts_path}")
                        await app.send_document(primary_chat_id, document=ts_path, progress=upload_progress)
                        logger.info(f"上传文档完成: {ts_path}")
                        
                        # 获取视频信息并上传视频
                        video_info = get_video_info(ts_path)
                        if video_info:
                            logger.info(f"开始上传视频: {mp4_path}")
                            await app.send_video(
                                primary_chat_id,
                                video=mp4_path,
                                duration=video_info['duration'],
                                width=video_info['width'],
                                height=video_info['height'],
                                thumb=video_info['thumb_path'],
                                supports_streaming=True,
                                caption=os.path.basename(mp4_path),
                                progress=upload_progress
                            )
                            logger.info(f"上传视频完成: {mp4_path}")
                            if video_info['thumb_path'] and os.path.exists(video_info['thumb_path']):
                                os.remove(video_info['thumb_path'])
                                logger.info(f"删除了缩略图: {video_info['thumb_path']}")
                    except Exception as e:
                        logger.error(f"上传文件时出错: {e}")
                    finally:
                        # 删除已处理的文件
                        if os.path.exists(ts_path):
                            os.remove(ts_path)
                            logger.info(f"删除了文件: {ts_path}")
                        if os.path.exists(mp4_path):
                            os.remove(mp4_path)
                            logger.info(f"删除了文件: {mp4_path}")
            else:
                logger.info("没有待处理的文件，等待10秒后再次检查。")
            await asyncio.sleep(10)
        except Exception as e:
            logger.error(f"处理上传时出错: {e}")
            await asyncio.sleep(10)

@app.on_message(filters.command("start") & filters.private)
async def start_handler(client, message):
    """
    等待第一个用户发送 /start 指令以建立会话，
    建立后启动文件扫描任务，其它用户将不被响应。
    """
    global primary_chat_id, scanning_task
    if primary_chat_id is None:
        primary_chat_id = message.chat.id
        await message.reply("会话建立成功，开始任务。")
        logger.info(f"会话建立，用户: {primary_chat_id}")
        scanning_task = asyncio.create_task(check_and_upload_new_files())
    else:
        if message.chat.id != primary_chat_id:
            await message.reply("目前只与第一个用户建立会话，其他用户暂不响应。")

@app.on_message(filters.text & filters.private)
async def url_handler(client, message):
    """
    识别用户发送的文本消息，如果内容为 URL，则追加到 config/URL_config.ini 文件末尾，
    但只处理与第一个用户的会话消息。
    """
    global primary_chat_id
    if primary_chat_id is None or message.chat.id != primary_chat_id:
        return
    text = message.text.strip()
    if is_valid_url(text):
        config_dir = os.path.join(os.getcwd(), "config")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        config_path = os.path.join(config_dir, "URL_config.ini")
        try:
            with open(config_path, "a", encoding="utf-8") as f:
                f.write(text + "\n")
            await message.reply("链接已成功添加到配置文件。")
        except Exception as e:
            logger.error(f"写入配置文件失败: {e}")
            await message.reply("添加链接时发生错误。")
    else:
        await message.reply("你发送的内容不是一个有效的 URL。")

def cleanup_session_files():
    """
    程序退出时自动删除生成的 Pyrogram session 文件。
    """
    session_files = glob.glob("my_subscription_bot.session*")
    for session_file in session_files:
        try:
            os.remove(session_file)
            logger.info(f"已删除 session 文件: {session_file}")
        except Exception as e:
            logger.error(f"删除 session 文件 {session_file} 失败: {e}")

# 注册程序退出时清理 session 文件
atexit.register(cleanup_session_files)

if __name__ == "__main__":
    app.start()
    logger.info("Bot 启动成功，等待 /start 指令建立会话。")
    idle()
    app.stop()
