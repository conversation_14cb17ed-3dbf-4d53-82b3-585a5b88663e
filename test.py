import json

# 定义 JSON 文件路径作为全局变量
json_file_path = '/home/<USER>/DLR/DLR/data.json'

def update_json_file(video_path):
    """
    更新 JSON 文件，添加新的视频文件路径到 "files" 键对应的列表中。
    :param video_path: 完成录制的视频文件路径
    """
    # 尝试读取现有的 JSON 文件内容
    try:
        with open(json_file_path, 'r') as file:
            data = json.load(file)
    except (FileNotFoundError, json.JSONDecodeError):
        # 如果文件不存在或读取错误，则初始化为包含空 "files" 列表的字典
        data = {"files": []}

    # 检查data是否含有"files"键，如果没有则添加
    if "files" not in data:
        data["files"] = []

    # 将新的视频文件路径添加到 "files" 键对应的列表中
    data["files"].append(video_path)
    
    # 将更新后的数据写回 JSON 文件
    with open(json_file_path, 'w') as file:
        json.dump(data, file, indent=4)

# 测试示例路径，请替换为实际路径
test_video_path = '/path/to/your/test_video.mp4'

# 调用函数进行测试
update_json_file(test_video_path)

print(f"已添加测试视频路径到 {json_file_path} 下的 'files' 列表中")
